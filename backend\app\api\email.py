from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.deps import get_current_admin_user
from app.models import User
from app.core.config_manager import ConfigManager
from pydantic import BaseModel
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

class BulkEmailRequest(BaseModel):
    subject: str
    content: str
    recipients: List[str]

@router.post("/bulk", summary="群发邮件")
async def send_bulk_email(
    email_data: BulkEmailRequest,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """发送群发邮件"""
    if not email_data.recipients:
        raise HTTPException(status_code=400, detail="收件人列表不能为空")
    
    # 获取SMTP配置
    config_manager = ConfigManager(db)
    smtp_config = config_manager.get_smtp_config()
    
    # 验证SMTP配置
    if not smtp_config['host'] or not smtp_config['username'] or not smtp_config['password']:
        raise HTTPException(status_code=400, detail="SMTP配置不完整，请先配置SMTP")
    
    sent_count = 0
    failed_count = 0
    
    # 连接SMTP服务器
    try:
        if smtp_config['use_ssl']:
            server = smtplib.SMTP_SSL(smtp_config['host'], smtp_config['port'])
        else:
            server = smtplib.SMTP(smtp_config['host'], smtp_config['port'])
            if smtp_config['use_tls']:
                server.starttls()
        
        server.login(smtp_config['username'], smtp_config['password'])
        
        # 批量发送邮件
        for recipient in email_data.recipients:
            try:
                msg = MIMEMultipart('alternative')
                msg['Subject'] = email_data.subject
                msg['From'] = f"{smtp_config['from_name']} <{smtp_config['from_address']}>"
                msg['To'] = recipient
                
                # 添加HTML内容
                html_part = MIMEText(email_data.content, 'html', 'utf-8')
                msg.attach(html_part)
                
                # 添加纯文本备份
                text_content = email_data.content.replace('<br>', '\n').replace('</p>', '\n').replace('<p>', '')
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)
                
                server.send_message(msg)
                sent_count += 1
                
            except Exception as e:
                logger.error(f"Failed to send email to {recipient}: {str(e)}")
                failed_count += 1
                continue
        
        server.quit()
        
    except Exception as e:
        logger.error(f"SMTP connection error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"邮件发送失败: {str(e)}")
    
    return {
        "message": f"邮件发送完成",
        "sent_count": sent_count,
        "failed_count": failed_count,
        "total": len(email_data.recipients)
    }

@router.post("/test", summary="发送测试邮件")
async def send_test_email(
    to_email: str,
    admin_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """发送测试邮件"""
    # 获取SMTP配置
    config_manager = ConfigManager(db)
    smtp_config = config_manager.get_smtp_config()
    
    # 验证SMTP配置
    if not smtp_config['host'] or not smtp_config['username'] or not smtp_config['password']:
        raise HTTPException(status_code=400, detail="SMTP配置不完整，请先配置SMTP")
    
    try:
        # 连接SMTP服务器
        if smtp_config['use_ssl']:
            server = smtplib.SMTP_SSL(smtp_config['host'], smtp_config['port'])
        else:
            server = smtplib.SMTP(smtp_config['host'], smtp_config['port'])
            if smtp_config['use_tls']:
                server.starttls()
        
        server.login(smtp_config['username'], smtp_config['password'])
        
        # 创建测试邮件
        msg = MIMEMultipart('alternative')
        msg['Subject'] = "测试邮件 - 动漫网站"
        msg['From'] = f"{smtp_config['from_name']} <{smtp_config['from_address']}>"
        msg['To'] = to_email
        
        # HTML内容
        html_content = """
        <html>
        <body>
            <h2>测试邮件</h2>
            <p>这是一封来自动漫网站的测试邮件。</p>
            <p>如果您收到这封邮件，说明SMTP配置正确。</p>
            <hr>
            <p><small>此邮件由系统自动发送，请勿回复。</small></p>
        </body>
        </html>
        """
        
        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(html_part)
        
        # 发送邮件
        server.send_message(msg)
        server.quit()
        
        return {"message": "测试邮件发送成功"}
        
    except Exception as e:
        logger.error(f"Test email failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试邮件发送失败: {str(e)}")
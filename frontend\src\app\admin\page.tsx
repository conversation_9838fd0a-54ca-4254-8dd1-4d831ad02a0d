'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Anime, CreateAnimeRequest, apiClient, Tag, FeaturedAnime, ConfigItem, SMTPConfig, Manga } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import { 
  Plus, Edit, Trash2, Save, X, Search, Users, BarChart3, 
  BookOpen, Megaphone, Globe, Settings, Film, Mail, 
  Bell, Code, MapPin, DollarSign, Shield, Clock, Zap,
  Eye, EyeOff, UserX, Trash, AlertCircle, CheckCircle
} from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';

// Import management components
import SEOConfiguration from '@/components/admin/SEOConfiguration';

interface AnimeFormData extends Omit<CreateAnimeRequest, 'tags'> {
  tags: string;
}

interface MangaFormData {
  title: string;
  description: string;
  cover: string;
  author: string;
  status: 'ongoing' | 'completed';
  categories: string;
  is_active: boolean;
}

interface AdPlacement {
  id: string;
  name: string;
  position: string;
  type: 'banner' | 'popup' | 'video' | 'sidebar' | 'native';
  enabled: boolean;
  html: string;
  javascript: string;
  showOnMobile: boolean;
  showOnDesktop: boolean;
  frequency?: number; // For popup ads
}

interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'popup' | 'email';
  target: 'all' | 'logged_in' | 'guests';
  is_active: boolean;
  created_at: string;
  sent_count?: number;
}

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  is_active: boolean;
  is_banned: boolean;
  created_at: string;
  last_login: string;
  favorite_count: number;
}

export default function AdminPage() {
  const [animes, setAnimes] = useState<Anime[]>([]);
  const [mangas, setMangas] = useState<Manga[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingAnime, setEditingAnime] = useState<Anime | null>(null);
  const [editingManga, setEditingManga] = useState<Manga | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAnimes, setTotalAnimes] = useState(0);
  const [totalMangas, setTotalMangas] = useState(0);
  const [contentTab, setContentTab] = useState('anime'); // Sub-tab for content management
  const [inactivityDays, setInactivityDays] = useState(365); // Customizable inactive period
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [userCurrentPage, setUserCurrentPage] = useState(1);
  const [userTotalPages, setUserTotalPages] = useState(1);
  const [userTotal, setUserTotal] = useState(0);
  const [userLoading, setUserLoading] = useState(false);
  const usersPerPage = 20;
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalAnimes: 0,
    totalMangas: 0,
    totalViews: 0,
    totalFavorites: 0,
    activeUsers: 0,
    bannedUsers: 0
  });

  // Advertisement placements configuration
  const [adPlacements, setAdPlacements] = useState<AdPlacement[]>([
    {
      id: 'homepage_banner',
      name: '首页顶部横幅',
      position: 'homepage_top',
      type: 'banner',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: true,
      showOnDesktop: true
    },
    {
      id: 'video_preroll',
      name: '视频前贴片广告',
      position: 'video_preroll',
      type: 'video',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: true,
      showOnDesktop: true
    },
    {
      id: 'sidebar_sticky',
      name: '侧边栏固定广告',
      position: 'sidebar',
      type: 'sidebar',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: false,
      showOnDesktop: true
    },
    {
      id: 'content_middle',
      name: '内容列表中间广告',
      position: 'content_list_middle',
      type: 'native',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: true,
      showOnDesktop: true
    },
    {
      id: 'popup_modal',
      name: '弹窗广告',
      position: 'modal',
      type: 'popup',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: true,
      showOnDesktop: true,
      frequency: 3 // Show every 3 page views
    },
    {
      id: 'manga_reader',
      name: '漫画阅读页底部',
      position: 'manga_reader_bottom',
      type: 'banner',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: true,
      showOnDesktop: true
    },
    {
      id: 'footer_banner',
      name: '页脚广告',
      position: 'footer',
      type: 'banner',
      enabled: false,
      html: '',
      javascript: '',
      showOnMobile: true,
      showOnDesktop: true
    }
  ]);

  // Featured management states
  const [featuredAnimes, setFeaturedAnimes] = useState<FeaturedAnime[]>([]);
  const [featuredLoading, setFeaturedLoading] = useState(false);
  const [featuredSearchTerm, setFeaturedSearchTerm] = useState('');
  const [featuredSearchResults, setFeaturedSearchResults] = useState<Anime[]>([]);
  const [editingFeatured, setEditingFeatured] = useState<FeaturedAnime | null>(null);
  const [customPosterUrl, setCustomPosterUrl] = useState('');

  // Edit states (editingAnime and editingManga already declared above)

  // Configuration management states
  const [configs, setConfigs] = useState<{[key: string]: {configs: {[key: string]: ConfigItem}}}>();
  const [configsLoading, setConfigsLoading] = useState(false);
  const [activeConfigTab, setActiveConfigTab] = useState('general');
  const [configChanges, setConfigChanges] = useState<{[key: string]: string}>({});
  const [smtpTesting, setSmtpTesting] = useState(false);
  const [emailTesting, setEmailTesting] = useState(false);
  const [smtpConfig, setSmtpConfig] = useState({
    host: '',
    port: 587,
    use_tls: true,
    use_ssl: false,
    username: '',
    password: '',
    sender_name: '动漫网站',
    sender_email: ''
  });
  const [testEmail, setTestEmail] = useState('');
  
  // 系统限制配置
  const [maxUserFavorites, setMaxUserFavorites] = useState('1000');

  // 安全设置配置
  const [securityConfig, setSecurityConfig] = useState({
    allow_registration: true,
    require_email_verification: false,
    enable_email_login: false,
    enable_registration_whitelist: false,
    registration_whitelist: '',
    turnstile_login_enabled: false,
    turnstile_register_enabled: false,
    turnstile_site_key: '',
    turnstile_secret_key: ''
  });
  const [securityConfigLoading, setSecurityConfigLoading] = useState(false);

  // 公告表单状态
  const [popupAnnouncementForm, setPopupAnnouncementForm] = useState({
    title: '',
    content: '',
    target: 'all' as 'all' | 'logged_in' | 'guests',
    auto_dismiss: true,
    auto_dismiss_seconds: 5,
    display_frequency: 'once' as 'once' | 'daily' | 'weekly' | 'always'
  });
  
  const [emailAnnouncementForm, setEmailAnnouncementForm] = useState({
    title: '',
    content: '',
    target: 'all' as 'all' | 'logged_in' | 'guests'
  });
  
  const [announcementSending, setAnnouncementSending] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<any>(null);
  const [inlineEditingId, setInlineEditingId] = useState<number | null>(null);
  const [inlineEditForm, setInlineEditForm] = useState<any>({});

  // Player configuration states
  const [playerConfig, setPlayerConfig] = useState({
    enable_ads: false,
    preroll_ad_url: '',
    midroll_ad_url: '',
    postroll_ad_url: '',
    enable_vast: false,
    vast_url: '',
    enable_rightclick: false,
    show_stats: false,
    show_version: false,
    skip_ad_time: 5,
    ad_volume: 0.7,
    autoplay: false,
    theme_color: '#7c3aed'
  });
  const [playerConfigLoading, setPlayerConfigLoading] = useState(false);

  const { isAdmin, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  const [animeFormData, setAnimeFormData] = useState<AnimeFormData>({
    title: '',
    title_english: '',
    description: '',
    category: '',
    tags: '',
    cover: '',
    fanart: '',
    video_url: '',
    release_year: new Date().getFullYear(),
  });

  const [mangaFormData, setMangaFormData] = useState<MangaFormData>({
    title: '',
    description: '',
    cover: '',
    author: '',
    status: 'ongoing',
    categories: '',
    is_active: true
  });

  const categories = ['动作', '冒险', '喜剧', '剧情', '奇幻', '恐怖', '浪漫', '科幻', '惊悚'];

  // 辅助函数：将Tag[]转换为逗号分隔的字符串
  const formatTagsForEdit = (tags?: Tag[] | string): string => {
    if (!tags) return '';
    if (typeof tags === 'string') return tags;
    if (Array.isArray(tags)) {
      return tags.map(tag => tag.name).join(', ');
    }
    return '';
  };

  useEffect(() => {
    // 等待认证加载完成
    if (authLoading) {
      return;
    }

    if (!isAuthenticated) {
      router.push('/auth');
      return;
    }
    if (!isAdmin) {
      router.push('/');
      return;
    }

    fetchAnimes();
    fetchMangas();
    fetchUsers(1, '');
    fetchStats();
    fetchFeaturedAnimes();
    fetchAnnouncements();
  }, [authLoading, isAuthenticated, isAdmin, currentPage, contentTab]);

  // 配置只在初始加载时获取一次
  useEffect(() => {
    if (!authLoading && isAuthenticated && isAdmin) {
      fetchConfigs();
      fetchPlayerConfig();
      fetchSmtpConfig();
    }
  }, [authLoading, isAuthenticated, isAdmin]);

  const fetchAnimes = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getAnimes({
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined
      });
      setAnimes(response.animes);
      setTotalPages(Math.ceil(response.total / 10));
      setTotalAnimes(response.total);
    } catch (error) {
      console.error('Failed to fetch animes:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMangas = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getMangas({
        skip: (currentPage - 1) * 10,
        limit: 10,
        search: searchTerm || undefined
      });
      setMangas(response.mangas || []);
      setTotalMangas(response.total || 0);
    } catch (error) {
      console.error('Failed to fetch mangas:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async (page: number = 1, search: string = '') => {
    try {
      setUserLoading(true);
      const skip = (page - 1) * usersPerPage;
      const response = await apiClient.getUsers(skip, usersPerPage, search);

      // 处理后端返回的数据格式
      if (response && typeof response === 'object' && 'users' in response) {
        setUsers(response.users || []);
        setUserTotal(response.total || 0);
        setUserTotalPages(Math.ceil((response.total || 0) / usersPerPage));
      } else if (Array.isArray(response)) {
        setUsers(response);
        setUserTotal(response.length);
        setUserTotalPages(1);
      } else {
        setUsers([]);
        setUserTotal(0);
        setUserTotalPages(1);
      }
      setUserCurrentPage(page);
    } catch (error) {
      console.error('Failed to fetch users:', error);
      setUsers([]);
      setUserTotal(0);
      setUserTotalPages(1);
    } finally {
      setUserLoading(false);
    }
  };

  const fetchAnnouncements = async () => {
    try {
      const response = await apiClient.getAnnouncements({ filter: 'all' });
      setAnnouncements(response || []);
    } catch (error) {
      console.error('Failed to fetch announcements:', error);
    }
  };

  const searchUsers = async () => {
    setUserCurrentPage(1);
    await fetchUsers(1, userSearchTerm);
  };

  const handleUserPageChange = async (page: number) => {
    await fetchUsers(page, userSearchTerm);
  };

  const fetchStats = async () => {
    try {
      const statsData = await apiClient.getAdminStats();
      setStats({
        totalUsers: statsData.total_users,
        totalAnimes: statsData.total_animes,
        totalMangas: statsData.total_mangas || 0,
        totalViews: statsData.total_views,
        totalFavorites: statsData.total_favorites,
        activeUsers: statsData.active_users || 0,
        bannedUsers: statsData.banned_users || 0
      });
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const fetchFeaturedAnimes = async () => {
    try {
      setFeaturedLoading(true);
      const featured = await apiClient.getAdminFeaturedAnimes();
      setFeaturedAnimes(featured);
    } catch (error) {
      console.error('Failed to fetch featured animes:', error);
      setFeaturedAnimes([]);
    } finally {
      setFeaturedLoading(false);
    }
  };

  const fetchConfigs = async () => {
    try {
      setConfigsLoading(true);
      const groupedConfigs = await apiClient.getGroupedConfigs();
      setConfigs(groupedConfigs);
      
      // 获取系统限制配置
      if (groupedConfigs.general?.configs?.max_favorite_count) {
        setMaxUserFavorites(groupedConfigs.general.configs.max_favorite_count.value || '1000');
      }

      // 获取安全配置
      if (groupedConfigs.users?.configs) {
        const userConfigs = groupedConfigs.users.configs;
        setSecurityConfig({
          allow_registration: userConfigs.allow_registration?.value === 'true',
          require_email_verification: userConfigs.require_email_verification?.value === 'true',
          enable_email_login: userConfigs.enable_email_login?.value === 'true',
          enable_registration_whitelist: userConfigs.enable_registration_whitelist?.value === 'true',
          registration_whitelist: userConfigs.registration_whitelist?.value || '',
          turnstile_login_enabled: userConfigs.turnstile_login_enabled?.value === 'true',
          turnstile_register_enabled: userConfigs.turnstile_register_enabled?.value === 'true',
          turnstile_site_key: userConfigs.turnstile_site_key?.value || '',
          turnstile_secret_key: userConfigs.turnstile_secret_key?.value || ''
        });
      }
    } catch (error) {
      console.error('Failed to fetch configs:', error);
    } finally {
      setConfigsLoading(false);
    }
  };

  const fetchPlayerConfig = async () => {
    try {
      setPlayerConfigLoading(true);
      const config = await apiClient.getAdminPlayerConfig();
      setPlayerConfig(config);
    } catch (error) {
      console.error('Failed to fetch player config:', error);
    } finally {
      setPlayerConfigLoading(false);
    }
  };

  const savePlayerConfig = async () => {
    try {
      setPlayerConfigLoading(true);
      // 将播放器配置转换为配置项格式
      const configUpdates = {
        'player_enable_ads': playerConfig.enable_ads.toString(),
        'player_preroll_ad_url': playerConfig.preroll_ad_url,
        'player_midroll_ad_url': playerConfig.midroll_ad_url,
        'player_postroll_ad_url': playerConfig.postroll_ad_url,
        'player_enable_vast': playerConfig.enable_vast.toString(),
        'player_vast_url': playerConfig.vast_url,
        'player_enable_rightclick': playerConfig.enable_rightclick.toString(),
        'player_show_stats': playerConfig.show_stats.toString(),
        'player_show_version': playerConfig.show_version.toString(),
        'player_skip_ad_time': playerConfig.skip_ad_time.toString(),
        'player_ad_volume': playerConfig.ad_volume.toString(),
        'player_autoplay': playerConfig.autoplay.toString(),
        'player_theme_color': playerConfig.theme_color
      };

      await apiClient.updateConfigsBatch(configUpdates);
      alert('播放器配置已保存');
      // 保存成功后保持当前状态，不重新获取配置
    } catch (error) {
      console.error('Failed to save player config:', error);
      alert('保存失败');
    } finally {
      setPlayerConfigLoading(false);
    }
  };
  
  // 保存全局收藏限制
  const handleSaveMaxFavorites = async () => {
    const value = parseInt(maxUserFavorites);
    if (isNaN(value) || value < 1 || value > 10000) {
      alert('请输入1-10000之间的有效数字');
      return;
    }
    
    try {
      await apiClient.updateConfigsBatch({
        max_favorite_count: maxUserFavorites
      });
      alert('收藏限制已更新');
      // 保存成功后保持当前状态，不重新获取配置
    } catch (error) {
      console.error('Failed to save max favorites:', error);
      alert('保存失败');
    }
  };

  // 保存安全配置
  const handleSaveSecurityConfig = async () => {
    try {
      setSecurityConfigLoading(true);
      
      // 验证白名单邮箱格式
      if (securityConfig.enable_registration_whitelist && securityConfig.registration_whitelist.trim()) {
        const entries = securityConfig.registration_whitelist.split(',').map(entry => entry.trim());
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
        const wildcardRegex = /^\*@[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
        
        const invalidEntries = entries.filter(entry => {
          // 检查是否为完整邮箱格式
          if (emailRegex.test(entry)) return false;
          // 检查是否为通配符邮箱格式 (如 *@qq.com)
          if (wildcardRegex.test(entry)) return false;
          // 检查是否为纯域名格式 (如 qq.com)
          if (domainRegex.test(entry)) return false;
          // 其他格式都视为无效
          return true;
        });
        
        if (invalidEntries.length > 0) {
          alert(`以下白名单条目格式无效: ${invalidEntries.join(', ')}\n\n支持的格式:\n• 完整邮箱: <EMAIL>\n• 通配符: *@example.com\n• 域名: example.com`);
          return;
        }
      }

      // 验证Turnstile配置
      if ((securityConfig.turnstile_login_enabled || securityConfig.turnstile_register_enabled) && 
          (!securityConfig.turnstile_site_key.trim() || !securityConfig.turnstile_secret_key.trim())) {
        alert('启用Turnstile验证时，站点密钥和密钥不能为空');
        return;
      }

      await apiClient.updateConfigsBatch({
        allow_registration: securityConfig.allow_registration.toString(),
        require_email_verification: securityConfig.require_email_verification.toString(),
        enable_email_login: securityConfig.enable_email_login.toString(),
        enable_registration_whitelist: securityConfig.enable_registration_whitelist.toString(),
        registration_whitelist: securityConfig.registration_whitelist,
        turnstile_login_enabled: securityConfig.turnstile_login_enabled.toString(),
        turnstile_register_enabled: securityConfig.turnstile_register_enabled.toString(),
        turnstile_site_key: securityConfig.turnstile_site_key,
        turnstile_secret_key: securityConfig.turnstile_secret_key
      });
      alert('安全配置已更新');
      // 保存成功后保持当前状态，不重新获取配置
    } catch (error) {
      console.error('Failed to save security config:', error);
      alert('保存失败');
    } finally {
      setSecurityConfigLoading(false);
    }
  };

  // User management functions
  const handleBanUser = async (userId: number, shouldBan: boolean) => {
    try {
      // shouldBan为true表示要封禁，所以is_active应该设为false
      await apiClient.updateUser(userId, { is_active: !shouldBan });
      fetchUsers(userCurrentPage, userSearchTerm);
      alert(shouldBan ? '用户已封禁' : '用户已解封');
    } catch (error) {
      console.error('Failed to update user ban status:', error);
      alert('操作失败');
    }
  };

  const handleDeleteUsers = async (userIds: number[]) => {
    if (!confirm(`确定要删除 ${userIds.length} 个用户吗？此操作不可撤销。`)) return;

    try {
      for (const userId of userIds) {
        await apiClient.deleteUser(userId);
      }
      fetchUsers(userCurrentPage, userSearchTerm);
      setSelectedUsers([]);
      alert('用户删除成功');
    } catch (error) {
      console.error('Failed to delete users:', error);
      alert('删除失败');
    }
  };

  // Anime management functions
  const handleEditAnime = (anime: Anime) => {
    setEditingAnime(anime);
    setAnimeFormData({
      title: anime.title,
      title_english: anime.title_english || '',
      description: anime.description || '',
      category: anime.category || '',
      tags: Array.isArray(anime.tags) ? anime.tags.map(t => t.name).join(', ') : '',
      cover: anime.cover || '',
      fanart: anime.fanart || '',
      video_url: anime.video_url || '',
      release_year: anime.release_year || new Date().getFullYear(),
    });
  };

  const handleUpdateAnime = async () => {
    if (!editingAnime) return;

    try {
      await apiClient.updateAnime(editingAnime.id, {
        title: animeFormData.title,
        title_english: animeFormData.title_english || null,
        description: animeFormData.description || null,
        category: animeFormData.category || null,
        tags: animeFormData.tags,
        cover: animeFormData.cover || null,
        fanart: animeFormData.fanart || null,
        video_url: animeFormData.video_url || null,
        release_year: animeFormData.release_year,
      });

      setEditingAnime(null);
      fetchAnimes();
      alert('动漫更新成功');
    } catch (error) {
      console.error('Failed to update anime:', error);
      alert('更新失败');
    }
  };

  const handleDeleteAnime = async (animeId: number) => {
    if (!confirm('确定要删除这部动漫吗？此操作不可撤销。')) return;

    try {
      await apiClient.deleteAnime(animeId);
      fetchAnimes();
      alert('动漫删除成功');
    } catch (error) {
      console.error('Failed to delete anime:', error);
      alert('删除失败');
    }
  };

  // Manga management functions
  const handleEditManga = (manga: Manga) => {
    setEditingManga(manga);
  };

  const handleUpdateManga = async (mangaData: any) => {
    if (!editingManga) return;

    try {
      await apiClient.updateManga(editingManga.id, mangaData);
      setEditingManga(null);
      fetchMangas();
      alert('漫画更新成功');
    } catch (error) {
      console.error('Failed to update manga:', error);
      alert('更新失败');
    }
  };

  const handleDeleteManga = async (mangaId: number) => {
    if (!confirm('确定要删除这部漫画吗？此操作不可撤销。')) return;

    try {
      await apiClient.deleteManga(mangaId);
      fetchMangas();
      alert('漫画删除成功');
    } catch (error) {
      console.error('Failed to delete manga:', error);
      alert('删除失败');
    }
  };

  const handleCleanupInactiveUsers = async () => {
    const inactiveUsers = users.filter(user => {
      const lastLogin = new Date(user.last_login);
      const daysSinceLogin = (Date.now() - lastLogin.getTime()) / (1000 * 60 * 60 * 24);
      return daysSinceLogin > inactivityDays;
    });

    if (inactiveUsers.length === 0) {
      alert('没有符合条件的不活跃用户');
      return;
    }

    if (!confirm(`发现 ${inactiveUsers.length} 个超过 ${inactivityDays} 天未登录的用户，确定要删除吗？`)) {
      return;
    }

    await handleDeleteUsers(inactiveUsers.map(u => u.id));
  };

  // Announcement functions
  const handleSendPopupAnnouncement = async () => {
    if (!popupAnnouncementForm.title.trim() || !popupAnnouncementForm.content.trim()) {
      alert('请填写完整的公告标题和内容');
      return;
    }

    try {
      setAnnouncementSending(true);
      
      if (editingAnnouncement) {
        // 更新已有公告
        await apiClient.updateAnnouncement(editingAnnouncement.id, {
          title: popupAnnouncementForm.title,
          content: popupAnnouncementForm.content,
          target: popupAnnouncementForm.target,
          type: 'popup',
          is_active: true,
          display_frequency: popupAnnouncementForm.display_frequency,
          auto_dismiss: popupAnnouncementForm.auto_dismiss,
          auto_dismiss_seconds: popupAnnouncementForm.auto_dismiss_seconds
        });
        alert('公告已更新');
        setEditingAnnouncement(null);
      } else {
        // 创建新公告
        await apiClient.createAnnouncement({
          title: popupAnnouncementForm.title,
          content: popupAnnouncementForm.content,
          target: popupAnnouncementForm.target,
          type: 'popup',
          is_active: true,
          display_frequency: popupAnnouncementForm.display_frequency,
          auto_dismiss: popupAnnouncementForm.auto_dismiss,
          auto_dismiss_seconds: popupAnnouncementForm.auto_dismiss_seconds
        });
        alert('弹窗公告已发布');
      }
      
      // 重置表单
      setPopupAnnouncementForm({
        title: '',
        content: '',
        target: 'all',
        auto_dismiss: true,
        auto_dismiss_seconds: 5,
        display_frequency: 'once'
      });
      
      fetchAnnouncements();
    } catch (error) {
      console.error('Failed to create/update popup announcement:', error);
      alert('操作失败，请稍后重试');
    } finally {
      setAnnouncementSending(false);
    }
  };

  const handleEditAnnouncement = (announcement: any) => {
    setEditingAnnouncement(announcement);
    setPopupAnnouncementForm({
      title: announcement.title,
      content: announcement.content,
      target: announcement.target,
      auto_dismiss: announcement.auto_dismiss ?? true,
      auto_dismiss_seconds: announcement.auto_dismiss_seconds || 5,
      display_frequency: announcement.display_frequency || 'once'
    });
  };
  
  const handleStartInlineEdit = (announcement: any) => {
    setInlineEditingId(announcement.id);
    setInlineEditForm({
      title: announcement.title,
      content: announcement.content,
      target: announcement.target,
      display_frequency: announcement.display_frequency || 'once'
    });
  };
  
  const handleSaveInlineEdit = async (announcementId: number) => {
    try {
      await apiClient.updateAnnouncement(announcementId, {
        ...inlineEditForm,
        type: 'popup',
        is_active: true
      });
      setInlineEditingId(null);
      setInlineEditForm({});
      fetchAnnouncements();
      alert('公告已更新');
    } catch (error) {
      console.error('Failed to update announcement:', error);
      alert('更新失败');
    }
  };
  
  const handleCancelInlineEdit = () => {
    setInlineEditingId(null);
    setInlineEditForm({});
  };

  const handleDeleteAnnouncement = async (announcementId: number) => {
    if (confirm('确定要删除这条公告吗？')) {
      try {
        await apiClient.deleteAnnouncement(announcementId);
        alert('公告已删除');
        fetchAnnouncements();
      } catch (error) {
        console.error('Failed to delete announcement:', error);
        alert('删除失败');
      }
    }
  };

  const handleToggleAnnouncement = async (announcementId: number) => {
    try {
      await apiClient.toggleAnnouncement(announcementId);
      fetchAnnouncements();
    } catch (error) {
      console.error('Failed to toggle announcement:', error);
      alert('操作失败');
    }
  };

  const handleSendEmailAnnouncement = async () => {
    if (!emailAnnouncementForm.title.trim() || !emailAnnouncementForm.content.trim()) {
      alert('请填写完整的邮件主题和内容');
      return;
    }

    try {
      setAnnouncementSending(true);
      const targetUsers = users.filter(u => {
        if (emailAnnouncementForm.target === 'all') return true;
        if (emailAnnouncementForm.target === 'logged_in') return u.last_login;
        if (emailAnnouncementForm.target === 'guests') return !u.last_login;
        return false;
      });

      if (targetUsers.length === 0) {
        alert('没有找到符合条件的用户');
        return;
      }

      await apiClient.sendBulkEmail({
        subject: emailAnnouncementForm.title,
        content: emailAnnouncementForm.content,
        recipients: targetUsers.map(u => u.email)
      });
      
      await apiClient.createAnnouncement({
        title: emailAnnouncementForm.title,
        content: emailAnnouncementForm.content,
        target: emailAnnouncementForm.target,
        type: 'email',
        sent_count: targetUsers.length,
        is_active: true
      });
      
      alert(`邮件公告已发送给 ${targetUsers.length} 个用户`);
      
      // 重置表单
      setEmailAnnouncementForm({
        title: '',
        content: '',
        target: 'all'
      });
      
      fetchAnnouncements();
    } catch (error) {
      console.error('Failed to send email announcement:', error);
      alert('发送失败，请检查SMTP配置或稍后重试');
    } finally {
      setAnnouncementSending(false);
    }
  };

  // SMTP configuration functions
  const fetchSmtpConfig = async () => {
    try {
      const config = await apiClient.getSmtpConfig();
      setSmtpConfig(config);
    } catch (error) {
      console.error('Failed to fetch SMTP config:', error);
    }
  };

  const saveSmtpConfig = async () => {
    try {
      await apiClient.updateSmtpConfig(smtpConfig);
      alert('SMTP配置保存成功');
      // 保存成功后保持当前状态，不重新获取配置
      // 配置会在页面刷新时从数据库重新加载
    } catch (error) {
      console.error('Failed to save SMTP config:', error);
      alert('保存失败');
    }
  };

  const testSmtpConnection = async () => {
    try {
      setSmtpTesting(true);
      await apiClient.testSmtpConnection(smtpConfig);
      alert('SMTP连接测试成功');
    } catch (error) {
      console.error('SMTP connection test failed:', error);
      alert('SMTP连接测试失败');
    } finally {
      setSmtpTesting(false);
    }
  };

  const sendTestEmail = async () => {
    if (!testEmail) {
      alert('请输入测试邮箱地址');
      return;
    }

    try {
      setEmailTesting(true);
      await apiClient.sendTestEmail({
        to: testEmail,
        subject: '测试邮件',
        content: '这是一封来自动漫网站的测试邮件，如果您收到此邮件，说明SMTP配置正确。'
      });
      alert('测试邮件发送成功');
    } catch (error) {
      console.error('Test email failed:', error);
      alert('测试邮件发送失败');
    } finally {
      setEmailTesting(false);
    }
  };

  // Ad placement functions
  const handleUpdateAdPlacement = (placementId: string, updates: Partial<AdPlacement>) => {
    setAdPlacements(prev => 
      prev.map(ad => ad.id === placementId ? { ...ad, ...updates } : ad)
    );
  };

  const handleSaveAdPlacements = async () => {
    try {
      await apiClient.updateAdPlacements(adPlacements);
      alert('广告配置已保存');
    } catch (error) {
      console.error('Failed to save ad placements:', error);
      alert('保存失败');
    }
  };
  
  // 配置获取已移至上面的useEffect中，避免重复

  // 认证加载中
  if (authLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            正在加载...
          </p>
        </div>
      </div>
    );
  }

  // 未认证或非管理员
  if (!isAuthenticated || !isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-lg text-muted-foreground">
            正在验证管理员权限...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">管理面板</h1>
          <p className="text-sm sm:text-base text-muted-foreground">管理网站内容和用户数据</p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 h-auto">
          <TabsTrigger value="overview" className="flex-col sm:flex-row gap-1 sm:gap-2 py-2 sm:py-1.5">
            <BarChart3 className="w-4 h-4" />
            <span className="text-xs sm:text-sm">概览</span>
          </TabsTrigger>
          <TabsTrigger value="content" className="flex-col sm:flex-row gap-1 sm:gap-2 py-2 sm:py-1.5">
            <Film className="w-4 h-4" />
            <span className="text-xs sm:text-sm">内容</span>
          </TabsTrigger>
          <TabsTrigger value="users" className="flex-col sm:flex-row gap-1 sm:gap-2 py-2 sm:py-1.5">
            <Users className="w-4 h-4" />
            <span className="text-xs sm:text-sm">用户</span>
          </TabsTrigger>
          <TabsTrigger value="announcements" className="flex-col sm:flex-row gap-1 sm:gap-2 py-2 sm:py-1.5">
            <Megaphone className="w-4 h-4" />
            <span className="text-xs sm:text-sm">公告</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex-col sm:flex-row gap-1 sm:gap-2 py-2 sm:py-1.5">
            <Settings className="w-4 h-4" />
            <span className="text-xs sm:text-sm">设置</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium">总用户数</CardTitle>
                <Users className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="pt-1 sm:pt-2">
                <div className="text-lg sm:text-2xl font-bold">{stats.totalUsers}</div>
                <p className="text-xs text-muted-foreground">
                  活跃: {stats.activeUsers} | 封禁: {stats.bannedUsers}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium">总动漫数</CardTitle>
                <Film className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="pt-1 sm:pt-2">
                <div className="text-lg sm:text-2xl font-bold">{stats.totalAnimes}</div>
                <p className="text-xs text-muted-foreground">已发布的动漫</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium">总漫画数</CardTitle>
                <BookOpen className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="pt-1 sm:pt-2">
                <div className="text-lg sm:text-2xl font-bold">{stats.totalMangas}</div>
                <p className="text-xs text-muted-foreground">已发布的漫画</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
                <CardTitle className="text-xs sm:text-sm font-medium">总观看数</CardTitle>
                <Eye className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="pt-1 sm:pt-2">
                <div className="text-lg sm:text-2xl font-bold">{stats.totalViews}</div>
                <p className="text-xs text-muted-foreground">累计观看次数</p>
              </CardContent>
            </Card>
          </div>

          {/* 最新数据 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 最新动漫 */}
            <Card>
              <CardHeader>
                <CardTitle>最新添加的动漫</CardTitle>
                <CardDescription>最近添加的5部动漫</CardDescription>
              </CardHeader>
              <CardContent>
                {animes && animes.length > 0 ? (
                  <div className="space-y-3">
                    {animes.slice(0, 5).map((anime) => (
                      <div key={anime.id} className="flex items-center gap-3 p-2 border rounded">
                        <img
                          src={anime.cover}
                          alt={anime.title}
                          className="w-12 h-16 object-cover rounded"
                        />
                        <div className="flex-1">
                          <p className="font-medium text-sm">{anime.title}</p>
                          <p className="text-xs text-muted-foreground">{anime.category}</p>
                          <p className="text-xs text-muted-foreground">
                            {anime.created_at ? format(new Date(anime.created_at), 'yyyy-MM-dd HH:mm') : '未知时间'}
                          </p>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          ID: {anime.id}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    暂无动漫数据
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 最新漫画 */}
            <Card>
              <CardHeader>
                <CardTitle>最新添加的漫画</CardTitle>
                <CardDescription>最近添加的5部漫画</CardDescription>
              </CardHeader>
              <CardContent>
                {mangas && mangas.length > 0 ? (
                  <div className="space-y-3">
                    {mangas.slice(0, 5).map((manga) => (
                      <div key={manga.id} className="flex items-center gap-3 p-2 border rounded">
                        <img
                          src={manga.cover}
                          alt={manga.title}
                          className="w-12 h-16 object-cover rounded"
                        />
                        <div className="flex-1">
                          <p className="font-medium text-sm">{manga.title}</p>
                          <p className="text-xs text-muted-foreground">{manga.author}</p>
                          <p className="text-xs text-muted-foreground">
                            {manga.created_at ? format(new Date(manga.created_at), 'yyyy-MM-dd HH:mm') : '未知时间'}
                          </p>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          ID: {manga.id}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-muted-foreground">
                    暂无漫画数据
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 最新用户 */}
          <Card>
            <CardHeader>
              <CardTitle>最新注册用户</CardTitle>
              <CardDescription>最近注册的10个用户</CardDescription>
            </CardHeader>
            <CardContent>
              {users && users.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {users.slice(0, 10).map((user) => (
                    <div key={user.id} className="flex items-center gap-3 p-3 border rounded">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{user.username}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                        <p className="text-xs text-muted-foreground">
                          {user.created_at ? format(new Date(user.created_at), 'yyyy-MM-dd') : '未知时间'}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <Badge variant={user.is_banned ? "destructive" : "secondary"} className="text-xs">
                          {user.is_banned ? "封禁" : "正常"}
                        </Badge>
                        <span className="text-xs text-muted-foreground">ID: {user.id}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  暂无用户数据
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Management Tab (Anime + Manga) */}
        <TabsContent value="content" className="space-y-6">
          <Tabs value={contentTab} onValueChange={setContentTab}>
            <TabsList>
              <TabsTrigger value="anime">动漫管理</TabsTrigger>
              <TabsTrigger value="manga">漫画管理</TabsTrigger>
              <TabsTrigger value="featured">推荐配置</TabsTrigger>
            </TabsList>

            {/* Anime Management */}
            <TabsContent value="anime" className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">动漫管理</h2>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  添加动漫
                </Button>
              </div>

              {/* Anime list */}
              <Card>
                <CardContent className="pt-6">
                  {loading ? (
                    <div className="text-center py-8">加载中...</div>
                  ) : animes && animes.length > 0 ? (
                    <div className="space-y-4">
                      {animes.map((anime) => (
                        <div key={anime.id} className="flex items-center gap-4 p-4 border rounded-lg">
                          <img
                            src={anime.cover || '/placeholder-anime.jpg'}
                            alt={anime.title}
                            className="w-16 h-20 object-cover rounded"
                          />
                          <div className="flex-1">
                            <h3 className="font-semibold">{anime.title}</h3>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>{anime.view_count} 观看</span>
                              <span>{anime.favorite_count} 收藏</span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditAnime(anime)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteAnime(anime.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">暂无动漫数据</div>
                  )}
                </CardContent>
              </Card>

              {/* 编辑动漫对话框 */}
              <Dialog open={!!editingAnime} onOpenChange={(open) => !open && setEditingAnime(null)}>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogClose onClose={() => setEditingAnime(null)} />
                  <DialogHeader>
                    <DialogTitle>编辑动漫</DialogTitle>
                    <DialogDescription>
                      修改动漫信息，点击保存后生效
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>标题</Label>
                        <Input
                          value={animeFormData.title}
                          onChange={(e) => setAnimeFormData(prev => ({ ...prev, title: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label>英文标题</Label>
                        <Input
                          value={animeFormData.title_english}
                          onChange={(e) => setAnimeFormData(prev => ({ ...prev, title_english: e.target.value }))}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>描述</Label>
                      <Textarea
                        value={animeFormData.description}
                        onChange={(e) => setAnimeFormData(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>分类</Label>
                        <Input
                          value={animeFormData.category}
                          onChange={(e) => setAnimeFormData(prev => ({ ...prev, category: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label>发布年份</Label>
                        <Input
                          type="number"
                          value={animeFormData.release_year}
                          onChange={(e) => setAnimeFormData(prev => ({ ...prev, release_year: parseInt(e.target.value) || new Date().getFullYear() }))}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>标签 (用逗号分隔)</Label>
                      <Input
                        value={animeFormData.tags}
                        onChange={(e) => setAnimeFormData(prev => ({ ...prev, tags: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label>封面图片URL</Label>
                      <Input
                        value={animeFormData.cover}
                        onChange={(e) => setAnimeFormData(prev => ({ ...prev, cover: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label>背景图片URL</Label>
                      <Input
                        value={animeFormData.fanart}
                        onChange={(e) => setAnimeFormData(prev => ({ ...prev, fanart: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label>视频URL</Label>
                      <Input
                        value={animeFormData.video_url}
                        onChange={(e) => setAnimeFormData(prev => ({ ...prev, video_url: e.target.value }))}
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button onClick={handleUpdateAnime}>
                        保存修改
                      </Button>
                      <Button variant="outline" onClick={() => setEditingAnime(null)}>
                        取消
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </TabsContent>

            {/* Manga Management */}
            <TabsContent value="manga" className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">漫画管理 (共{totalMangas}部)</h2>
                <Button onClick={() => setIsCreating(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  添加漫画
                </Button>
              </div>

              {/* Current manga list */}
              <Card>
                <CardContent className="pt-6">
                  {loading ? (
                    <div className="text-center py-8">加载中...</div>
                  ) : mangas && mangas.length > 0 ? (
                    <div className="space-y-4">
                      {mangas.map((manga) => (
                        <div key={manga.id} className="flex items-center gap-4 p-4 border rounded-lg">
                          <img
                            src={manga.cover || '/placeholder-manga.jpg'}
                            alt={manga.title}
                            className="w-16 h-20 object-cover rounded"
                          />
                          <div className="flex-1">
                            <h3 className="font-semibold">{manga.title}</h3>
                            <p className="text-sm text-muted-foreground">{manga.description}</p>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                              <Badge variant={manga.status === 'completed' ? 'success' : 'default'}>
                                {manga.status === 'completed' ? '已完结' : '连载中'}
                              </Badge>
                              {manga.category && (
                                <span>分类: {manga.category.name}</span>
                              )}
                              <span>作者: {manga.author || '未知'}</span>
                              <span>章节: {manga.chapter_count || 0}</span>
                              <span>收藏: {manga.favorite_count || 0}</span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditManga(manga)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteManga(manga.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">暂无漫画数据</p>
                      <Button className="mt-4" onClick={() => setIsCreating(true)}>
                        <Plus className="w-4 h-4 mr-2" />
                        添加第一部漫画
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 编辑漫画对话框 */}
              <Dialog open={!!editingManga} onOpenChange={(open) => !open && setEditingManga(null)}>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogClose onClose={() => setEditingManga(null)} />
                  <DialogHeader>
                    <DialogTitle>编辑漫画</DialogTitle>
                    <DialogDescription>
                      修改漫画信息，点击保存后生效
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>标题</Label>
                        <Input
                          defaultValue={editingManga?.title}
                          onChange={(e) => setEditingManga(prev => prev ? { ...prev, title: e.target.value } : null)}
                        />
                      </div>
                      <div>
                        <Label>作者</Label>
                        <Input
                          defaultValue={editingManga?.author || ''}
                          onChange={(e) => setEditingManga(prev => prev ? { ...prev, author: e.target.value } : null)}
                        />
                      </div>
                    </div>
                    <div>
                      <Label>描述</Label>
                      <Textarea
                        defaultValue={editingManga?.description || ''}
                        onChange={(e) => setEditingManga(prev => prev ? { ...prev, description: e.target.value } : null)}
                        rows={3}
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>分类</Label>
                        <Input
                          defaultValue={editingManga?.category?.name || ''}
                          onChange={(e) => setEditingManga(prev => prev ? {
                            ...prev,
                            category: prev.category ? { ...prev.category, name: e.target.value } : { id: 0, name: e.target.value, num: 0, created_at: '', updated_at: '' }
                          } : null)}
                        />
                      </div>
                      <div>
                        <Label>状态</Label>
                        <Select
                          defaultValue={editingManga?.status || 'ongoing'}
                          onValueChange={(value) => setEditingManga(prev => prev ? { ...prev, status: value } : null)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ongoing">连载中</SelectItem>
                            <SelectItem value="completed">已完结</SelectItem>
                            <SelectItem value="hiatus">暂停</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <Label>封面图片URL</Label>
                      <Input
                        defaultValue={editingManga?.cover || ''}
                        onChange={(e) => setEditingManga(prev => prev ? { ...prev, cover: e.target.value } : null)}
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button
                        onClick={() => {
                          if (editingManga) {
                            handleUpdateManga({
                              title: editingManga.title,
                              author: editingManga.author,
                              description: editingManga.description,
                              category_id: editingManga.category?.id,
                              status: editingManga.status,
                              cover: editingManga.cover
                            });
                          }
                        }}
                      >
                        保存修改
                      </Button>
                      <Button variant="outline" onClick={() => setEditingManga(null)}>
                        取消
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </TabsContent>

            {/* Featured Management */}
            <TabsContent value="featured" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>推荐配置</CardTitle>
                  <CardDescription>管理首页推荐内容</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 添加推荐 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">添加推荐</h3>
                    <div className="flex gap-2">
                      <Input
                        placeholder="搜索动漫..."
                        value={featuredSearchTerm}
                        onChange={(e) => setFeaturedSearchTerm(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && featuredSearchTerm.trim()) {
                            // 触发搜索
                            const searchButton = e.currentTarget.nextElementSibling as HTMLButtonElement;
                            searchButton?.click();
                          }
                        }}
                        className="flex-1"
                      />
                      <Button
                        onClick={async () => {
                          if (featuredSearchTerm.trim()) {
                            try {
                              const results = await apiClient.searchAnimes(featuredSearchTerm, { skip: 0, limit: 10 });
                              setFeaturedSearchResults(results);
                            } catch (error) {
                              console.error('Search failed:', error);
                              alert('搜索失败，请重试');
                            }
                          }
                        }}
                      >
                        <Search className="w-4 h-4" />
                      </Button>
                      {featuredSearchResults.length > 0 && (
                        <Button
                          variant="outline"
                          onClick={() => {
                            setFeaturedSearchResults([]);
                            setFeaturedSearchTerm('');
                          }}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>

                    {/* 搜索结果 */}
                    {featuredSearchResults && featuredSearchResults.length > 0 && (
                      <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
                        <h4 className="font-medium mb-2">搜索结果</h4>
                        <div className="space-y-2">
                          {featuredSearchResults.map((anime) => (
                            <div key={anime.id} className="flex items-center justify-between p-2 border rounded">
                              <div className="flex items-center gap-3">
                                <img
                                  src={anime.cover}
                                  alt={anime.title}
                                  className="w-12 h-16 object-cover rounded"
                                />
                                <div>
                                  <p className="font-medium">{anime.title}</p>
                                  <p className="text-sm text-muted-foreground">{anime.category}</p>
                                </div>
                              </div>
                              <Button
                                size="sm"
                                onClick={async () => {
                                  try {
                                    await apiClient.addFeaturedAnime(
                                      anime.id,
                                      (featuredAnimes?.length || 0) + 1,
                                      null
                                    );
                                    fetchFeaturedAnimes();
                                    setFeaturedSearchResults([]);
                                    setFeaturedSearchTerm('');
                                    alert('添加成功');
                                  } catch (error) {
                                    console.error('Failed to add featured:', error);
                                    alert('添加失败');
                                  }
                                }}
                              >
                                <Plus className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 当前推荐列表 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">当前推荐 ({featuredAnimes?.length || 0})</h3>
                    {featuredLoading ? (
                      <div className="text-center py-4">加载中...</div>
                    ) : !featuredAnimes || featuredAnimes.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        暂无推荐内容
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {featuredAnimes.map((featured, index) => (
                          <div key={featured.id} className="flex items-center gap-4 p-4 border rounded-lg">
                            <div className="flex items-center gap-3 flex-1">
                              <span className="text-sm font-medium w-8">#{featured.order_index}</span>
                              <img
                                src={featured.custom_poster_url || featured.anime?.cover}
                                alt={featured.anime?.title}
                                className="w-12 h-16 object-cover rounded"
                              />
                              <div className="flex-1">
                                <p className="font-medium">{featured.anime?.title}</p>
                                <p className="text-sm text-muted-foreground">{featured.anime?.category}</p>
                                {featured.custom_poster_url && (
                                  <p className="text-xs text-blue-600">使用自定义海报</p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setEditingFeatured(featured)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  if (index > 0) {
                                    try {
                                      await apiClient.updateFeaturedAnime(featured.id, {
                                        order_index: featured.order_index - 1
                                      });
                                      fetchFeaturedAnimes();
                                    } catch (error) {
                                      console.error('Failed to move up:', error);
                                    }
                                  }
                                }}
                                disabled={index === 0}
                              >
                                ↑
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  if (featuredAnimes && index < featuredAnimes.length - 1) {
                                    try {
                                      await apiClient.updateFeaturedAnime(featured.id, {
                                        order_index: featured.order_index + 1
                                      });
                                      fetchFeaturedAnimes();
                                    } catch (error) {
                                      console.error('Failed to move down:', error);
                                    }
                                  }
                                }}
                                disabled={!featuredAnimes || index === featuredAnimes.length - 1}
                              >
                                ↓
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={async () => {
                                  if (confirm('确定要移除这个推荐吗？')) {
                                    try {
                                      await apiClient.removeFeaturedAnime(featured.id);
                                      fetchFeaturedAnimes();
                                      alert('移除成功');
                                    } catch (error) {
                                      console.error('Failed to remove featured:', error);
                                      alert('移除失败');
                                    }
                                  }
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 编辑推荐对话框 */}
              <Dialog open={!!editingFeatured} onOpenChange={(open) => {
                if (!open) {
                  setEditingFeatured(null);
                  setCustomPosterUrl('');
                }
              }}>
                <DialogContent className="max-w-lg">
                  <DialogClose onClose={() => {
                    setEditingFeatured(null);
                    setCustomPosterUrl('');
                  }} />
                  <DialogHeader>
                    <DialogTitle>编辑推荐</DialogTitle>
                    <DialogDescription>
                      修改推荐动漫的显示设置
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label>动漫标题</Label>
                      <Input value={editingFeatured?.anime?.title || ''} disabled />
                    </div>
                    <div>
                      <Label>排序位置</Label>
                      <Input
                        type="number"
                        value={editingFeatured?.order_index || 1}
                        onChange={(e) => setEditingFeatured(prev => prev ? {
                          ...prev,
                          order_index: parseInt(e.target.value) || 1
                        } : null)}
                        min="1"
                      />
                    </div>
                    <div>
                      <Label>自定义海报URL（可选）</Label>
                      <Input
                        value={customPosterUrl || editingFeatured?.custom_poster_url || ''}
                        onChange={(e) => setCustomPosterUrl(e.target.value)}
                        placeholder="留空使用默认海报"
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button
                        onClick={async () => {
                          if (!editingFeatured) return;
                          try {
                            await apiClient.updateFeaturedAnime(editingFeatured.id, {
                              order_index: editingFeatured.order_index,
                              custom_poster_url: customPosterUrl || null
                            });
                            fetchFeaturedAnimes();
                            setEditingFeatured(null);
                            setCustomPosterUrl('');
                            alert('更新成功');
                          } catch (error) {
                            console.error('Failed to update featured:', error);
                            alert('更新失败');
                          }
                        }}
                      >
                        保存
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setEditingFeatured(null);
                          setCustomPosterUrl('');
                        }}
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* User Management Tab */}
        <TabsContent value="users" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>用户管理</CardTitle>
                    <CardDescription>
                      {userLoading ? '加载中...' : `共 ${userTotal} 个用户，当前显示第 ${userCurrentPage} 页`}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <div className="flex items-center gap-2">
                      <Label>不活跃天数:</Label>
                      <Input
                        type="number"
                        value={inactivityDays}
                        onChange={(e) => setInactivityDays(parseInt(e.target.value) || 365)}
                        className="w-20"
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={handleCleanupInactiveUsers}
                    >
                      <Trash className="w-4 h-4 mr-2" />
                      清理不活跃用户
                    </Button>
                  </div>
                </div>

                {/* 用户搜索 */}
                <div className="flex gap-2">
                  <Input
                    placeholder="搜索用户名或邮箱..."
                    value={userSearchTerm}
                    onChange={(e) => setUserSearchTerm(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        searchUsers();
                      }
                    }}
                    className="flex-1"
                  />
                  <Button onClick={searchUsers} disabled={userLoading}>
                    <Search className="w-4 h-4" />
                  </Button>
                  {userSearchTerm && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setUserSearchTerm('');
                        setUserCurrentPage(1);
                        fetchUsers(1, '');
                      }}
                      disabled={userLoading}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users && users.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">
                            <Checkbox 
                              checked={users && selectedUsers && selectedUsers.length === users.length}
                              onCheckedChange={(checked) => {
                                setSelectedUsers(checked && users ? users.map(u => u.id) : []);
                              }}
                            />
                          </th>
                          <th className="text-left p-2">用户名</th>
                          <th className="text-left p-2">邮箱</th>
                          <th className="text-left p-2">角色</th>
                          <th className="text-left p-2">状态</th>
                          <th className="text-left p-2">注册时间</th>
                          <th className="text-left p-2">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        {users.map((user) => (
                          <tr key={user.id} className="border-b">
                            <td className="p-2">
                              <Checkbox 
                                checked={selectedUsers.includes(user.id)}
                                onCheckedChange={(checked) => {
                                  setSelectedUsers(prev => 
                                    checked 
                                      ? [...prev, user.id]
                                      : prev.filter(id => id !== user.id)
                                  );
                                }}
                              />
                            </td>
                            <td className="p-2">{user.username}</td>
                            <td className="p-2">{user.email}</td>
                            <td className="p-2">
                              <Badge variant={user.is_admin ? "default" : "secondary"}>
                                {user.is_admin ? "管理员" : "用户"}
                              </Badge>
                            </td>
                            <td className="p-2">
                              {!user.is_active ? (
                                <Badge variant="destructive">已封禁</Badge>
                              ) : (
                                <Badge variant="secondary">正常</Badge>
                              )}
                            </td>
                            <td className="p-2 text-sm">
                              {user.created_at ? format(new Date(user.created_at), 'yyyy-MM-dd') : '未知'}
                            </td>
                            <td className="p-2">
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant={!user.is_active ? "outline" : "destructive"}
                                  onClick={() => handleBanUser(user.id, user.is_active)}
                                >
                                  {!user.is_active ? (
                                    <>
                                      <CheckCircle className="w-3 h-3 mr-1" />
                                      解封
                                    </>
                                  ) : (
                                    <>
                                      <UserX className="w-3 h-3 mr-1" />
                                      封禁
                                    </>
                                  )}
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">暂无用户数据</p>
                  </div>
                )}

                {/* 分页组件 */}
                {userTotalPages > 1 && (
                  <div className="flex justify-center items-center gap-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserPageChange(userCurrentPage - 1)}
                      disabled={userCurrentPage <= 1 || userLoading}
                    >
                      上一页
                    </Button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, userTotalPages) }, (_, i) => {
                        let pageNum;
                        if (userTotalPages <= 5) {
                          pageNum = i + 1;
                        } else if (userCurrentPage <= 3) {
                          pageNum = i + 1;
                        } else if (userCurrentPage >= userTotalPages - 2) {
                          pageNum = userTotalPages - 4 + i;
                        } else {
                          pageNum = userCurrentPage - 2 + i;
                        }

                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === userCurrentPage ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleUserPageChange(pageNum)}
                            disabled={userLoading}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUserPageChange(userCurrentPage + 1)}
                      disabled={userCurrentPage >= userTotalPages || userLoading}
                    >
                      下一页
                    </Button>

                    <span className="text-sm text-muted-foreground ml-2">
                      第 {userCurrentPage} 页，共 {userTotalPages} 页
                    </span>
                  </div>
                )}

                {selectedUsers && selectedUsers.length > 0 && (
                  <div className="flex justify-between items-center p-4 bg-muted rounded">
                    <span>已选择 {selectedUsers.length} 个用户</span>
                    <Button
                      variant="destructive"
                      onClick={() => handleDeleteUsers(selectedUsers)}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      删除选中用户
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Announcement System Tab */}
        <TabsContent value="announcements" className="space-y-6">
          <Tabs defaultValue="popup">
            <TabsList>
              <TabsTrigger value="popup">
                <Bell className="w-4 h-4 mr-2" />
                弹窗公告
              </TabsTrigger>
              <TabsTrigger value="email">
                <Mail className="w-4 h-4 mr-2" />
                邮件公告
              </TabsTrigger>
              <TabsTrigger value="history">
                <Clock className="w-4 h-4 mr-2" />
                历史记录
              </TabsTrigger>
            </TabsList>

            {/* Popup Announcements */}
            <TabsContent value="popup" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>{editingAnnouncement ? '编辑弹窗公告' : '创建弹窗公告'}</CardTitle>
                  <CardDescription>在网站上显示弹窗通知</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>公告标题</Label>
                    <Input 
                      placeholder="输入公告标题"
                      value={popupAnnouncementForm.title}
                      onChange={(e) => setPopupAnnouncementForm(prev => ({
                        ...prev,
                        title: e.target.value
                      }))}
                    />
                  </div>
                  <div>
                    <Label>公告内容</Label>
                    <Textarea 
                      rows={4} 
                      placeholder="输入公告内容"
                      value={popupAnnouncementForm.content}
                      onChange={(e) => setPopupAnnouncementForm(prev => ({
                        ...prev,
                        content: e.target.value
                      }))}
                    />
                  </div>
                  <div>
                    <Label>目标用户</Label>
                    <Select 
                      value={popupAnnouncementForm.target}
                      onValueChange={(value: 'all' | 'logged_in' | 'guests') => 
                        setPopupAnnouncementForm(prev => ({
                          ...prev,
                          target: value
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择目标用户" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有用户</SelectItem>
                        <SelectItem value="logged_in">登录用户</SelectItem>
                        <SelectItem value="guests">游客</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>显示频率</Label>
                    <Select 
                      value={popupAnnouncementForm.display_frequency}
                      onValueChange={(value: 'once' | 'daily' | 'weekly' | 'always') => 
                        setPopupAnnouncementForm(prev => ({
                          ...prev,
                          display_frequency: value
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择显示频率" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="once">仅显示一次</SelectItem>
                        <SelectItem value="daily">每天显示一次</SelectItem>
                        <SelectItem value="weekly">每周显示一次</SelectItem>
                        <SelectItem value="always">每次刷新都显示</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Switch 
                        id="auto-dismiss"
                        checked={popupAnnouncementForm.auto_dismiss}
                        onCheckedChange={(checked) => setPopupAnnouncementForm(prev => ({
                          ...prev,
                          auto_dismiss: checked
                        }))}
                      />
                      <Label htmlFor="auto-dismiss">自动关闭公告</Label>
                    </div>
                    {popupAnnouncementForm.auto_dismiss && (
                      <div className="flex items-center gap-2 ml-6">
                        <Label>关闭延迟：</Label>
                        <Input
                          type="number"
                          min="1"
                          max="60"
                          value={popupAnnouncementForm.auto_dismiss_seconds}
                          onChange={(e) => setPopupAnnouncementForm(prev => ({
                            ...prev,
                            auto_dismiss_seconds: parseInt(e.target.value) || 5
                          }))}
                          className="w-20"
                        />
                        <span className="text-sm text-muted-foreground">秒</span>
                      </div>
                    )}
                  </div>
                  {editingAnnouncement && (
                    <Button 
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        setEditingAnnouncement(null);
                        setPopupAnnouncementForm({
                          title: '',
                          content: '',
                          target: 'all',
                          auto_dismiss: true,
                          auto_dismiss_seconds: 5,
                          display_frequency: 'once'
                        });
                      }}
                    >
                      取消编辑
                    </Button>
                  )}
                  <Button 
                    className="w-full"
                    onClick={handleSendPopupAnnouncement}
                    disabled={announcementSending}
                  >
                    {announcementSending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        {editingAnnouncement ? '更新中...' : '发布中...'}
                      </>
                    ) : (
                      <>
                        <Bell className="w-4 h-4 mr-2" />
                        {editingAnnouncement ? '更新公告' : '发布弹窗公告'}
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Email Announcements */}
            <TabsContent value="email" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>群发邮件公告</CardTitle>
                  <CardDescription>向用户发送邮件通知</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>邮件主题</Label>
                    <Input 
                      placeholder="输入邮件主题"
                      value={emailAnnouncementForm.title}
                      onChange={(e) => setEmailAnnouncementForm(prev => ({
                        ...prev,
                        title: e.target.value
                      }))}
                    />
                  </div>
                  <div>
                    <Label>邮件内容</Label>
                    <Textarea 
                      rows={6} 
                      placeholder="输入邮件内容（支持HTML）"
                      value={emailAnnouncementForm.content}
                      onChange={(e) => setEmailAnnouncementForm(prev => ({
                        ...prev,
                        content: e.target.value
                      }))}
                    />
                  </div>
                  <div>
                    <Label>收件人</Label>
                    <Select
                      value={emailAnnouncementForm.target}
                      onValueChange={(value: 'all' | 'logged_in' | 'guests') => 
                        setEmailAnnouncementForm(prev => ({
                          ...prev,
                          target: value
                        }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择收件人群体" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">所有用户 ({users?.length || 0})</SelectItem>
                        <SelectItem value="logged_in">登录用户</SelectItem>
                        <SelectItem value="guests">游客</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center gap-2 text-amber-800">
                      <AlertCircle className="w-4 h-4" />
                      <span className="font-medium">注意事项</span>
                    </div>
                    <p className="text-sm text-amber-700 mt-1">
                      群发邮件将消耗服务器资源，建议分批发送。请确保SMTP配置正确。
                    </p>
                  </div>
                  <Button 
                    className="w-full"
                    onClick={handleSendEmailAnnouncement}
                    disabled={announcementSending}
                  >
                    {announcementSending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        发送中...
                      </>
                    ) : (
                      <>
                        <Mail className="w-4 h-4 mr-2" />
                        发送邮件公告
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Announcement History */}
            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>公告历史</CardTitle>
                </CardHeader>
                <CardContent>
                  {announcements && announcements.length > 0 ? (
                    <div className="space-y-2">
                      {announcements.map((announcement) => (
                        <div key={announcement.id} className="p-3 border rounded-lg">
                          {inlineEditingId === announcement.id ? (
                            // 内联编辑模式
                            <div className="space-y-3">
                              <div>
                                <Label>标题</Label>
                                <Input
                                  value={inlineEditForm.title || ''}
                                  onChange={(e) => setInlineEditForm(prev => ({
                                    ...prev,
                                    title: e.target.value
                                  }))}
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label>内容</Label>
                                <Textarea
                                  value={inlineEditForm.content || ''}
                                  onChange={(e) => setInlineEditForm(prev => ({
                                    ...prev,
                                    content: e.target.value
                                  }))}
                                  rows={3}
                                  className="mt-1"
                                />
                              </div>
                              <div className="grid grid-cols-2 gap-2">
                                <div>
                                  <Label>目标用户</Label>
                                  <Select
                                    value={inlineEditForm.target}
                                    onValueChange={(value) => setInlineEditForm(prev => ({
                                      ...prev,
                                      target: value
                                    }))}
                                  >
                                    <SelectTrigger className="mt-1">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="all">所有用户</SelectItem>
                                      <SelectItem value="logged_in">登录用户</SelectItem>
                                      <SelectItem value="guests">游客</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label>显示频率</Label>
                                  <Select
                                    value={inlineEditForm.display_frequency}
                                    onValueChange={(value) => setInlineEditForm(prev => ({
                                      ...prev,
                                      display_frequency: value
                                    }))}
                                  >
                                    <SelectTrigger className="mt-1">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="once">仅一次</SelectItem>
                                      <SelectItem value="daily">每日</SelectItem>
                                      <SelectItem value="weekly">每周</SelectItem>
                                      <SelectItem value="always">总是</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                              <div className="flex justify-end gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={handleCancelInlineEdit}
                                >
                                  <X className="w-4 h-4 mr-1" />
                                  取消
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleSaveInlineEdit(announcement.id)}
                                >
                                  <Save className="w-4 h-4 mr-1" />
                                  保存
                                </Button>
                              </div>
                            </div>
                          ) : (
                            // 显示模式
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h4 className="font-medium">{announcement.title}</h4>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {announcement.content}
                                </p>
                                <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                  <Badge variant={announcement.type === 'popup' ? 'default' : 'secondary'}>
                                    {announcement.type === 'popup' ? '弹窗' : '邮件'}
                                  </Badge>
                                  <Badge variant={announcement.is_active ? 'default' : 'outline'}>
                                    {announcement.is_active ? '启用' : '停用'}
                                  </Badge>
                                  <Badge variant="outline">
                                    {announcement.target === 'all' ? '所有用户' : announcement.target === 'logged_in' ? '登录用户' : '游客'}
                                  </Badge>
                                  {announcement.display_frequency && (
                                    <Badge variant="outline">
                                      {announcement.display_frequency === 'once' ? '仅一次' : 
                                       announcement.display_frequency === 'daily' ? '每日' :
                                       announcement.display_frequency === 'weekly' ? '每周' : '总是'}
                                    </Badge>
                                  )}
                                  <span>{announcement.created_at ? format(new Date(announcement.created_at), 'yyyy-MM-dd HH:mm') : '未知时间'}</span>
                                  {announcement.sent_count && (
                                    <span>已发送: {announcement.sent_count}</span>
                                  )}
                                </div>
                              </div>
                              <div className="flex gap-2">
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => handleToggleAnnouncement(announcement.id)}
                                  title={announcement.is_active ? '停用公告' : '启用公告'}
                                >
                                  {announcement.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => handleStartInlineEdit(announcement)}
                                  title="编辑公告"
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  variant="ghost"
                                  onClick={() => handleDeleteAnnouncement(announcement.id)}
                                  title="删除公告"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-muted-foreground py-8">暂无公告记录</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* System Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="general">网站信息</TabsTrigger>
              <TabsTrigger value="email">邮件配置</TabsTrigger>
              <TabsTrigger value="security">安全设置</TabsTrigger>
              <TabsTrigger value="seo">SEO设置</TabsTrigger>
              <TabsTrigger value="ads">广告配置</TabsTrigger>
              <TabsTrigger value="player">播放器</TabsTrigger>
            </TabsList>

            {/* General Settings */}
            <TabsContent value="general" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>网站基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>网站名称</Label>
                      <Input placeholder="视频站" />
                    </div>
                    <div>
                      <Label>网站URL</Label>
                      <Input placeholder="https://example.com" />
                    </div>
                    <div>
                      <Label>联系邮箱</Label>
                      <Input type="email" placeholder="<EMAIL>" />
                    </div>
                    <div>
                      <Label>ICP备案号</Label>
                      <Input placeholder="京ICP备xxxxx号" />
                    </div>
                  </div>
                  <div>
                    <Label>网站描述</Label>
                    <Textarea rows={3} placeholder="网站描述信息" />
                  </div>
                  <Button>保存设置</Button>
                </CardContent>
              </Card>
              
              {/* 系统限制设置 */}
              <Card>
                <CardHeader>
                  <CardTitle>系统限制设置</CardTitle>
                  <CardDescription>配置系统全局限制参数</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>用户最大收藏数量</Label>
                    <div className="flex items-center gap-4">
                      <Input 
                        type="number"
                        min="1"
                        max="10000"
                        placeholder="1000"
                        value={maxUserFavorites}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === '' || (parseInt(value) >= 1 && parseInt(value) <= 10000)) {
                            setMaxUserFavorites(value);
                          }
                        }}
                        className="w-32"
                      />
                      <span className="text-sm text-muted-foreground">设置用户可以收藏的最大数量（适用于所有用户）</span>
                    </div>
                  </div>
                  <Button onClick={handleSaveMaxFavorites}>保存收藏限制</Button>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Email Settings */}
            <TabsContent value="email" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>SMTP邮件配置</CardTitle>
                  <CardDescription>配置SMTP服务器以发送邮件通知和公告</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* SMTP服务器配置 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">服务器设置</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>SMTP服务器地址 *</Label>
                        <Input
                          placeholder="smtp.gmail.com"
                          value={smtpConfig.host}
                          onChange={(e) => setSmtpConfig(prev => ({ ...prev, host: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label>SMTP端口 *</Label>
                        <Input
                          type="number"
                          placeholder="587"
                          value={smtpConfig.port}
                          onChange={(e) => setSmtpConfig(prev => ({ ...prev, port: parseInt(e.target.value) || 587 }))}
                        />
                      </div>
                    </div>

                    {/* 加密设置 */}
                    <div className="space-y-3">
                      <h4 className="font-medium">加密设置</h4>
                      <div className="flex items-center gap-6">
                        <div className="flex items-center gap-2">
                          <Switch
                            id="smtp-tls"
                            checked={smtpConfig.use_tls}
                            onCheckedChange={(checked) => setSmtpConfig(prev => ({ ...prev, use_tls: checked }))}
                          />
                          <Label htmlFor="smtp-tls">使用TLS加密</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            id="smtp-ssl"
                            checked={smtpConfig.use_ssl}
                            onCheckedChange={(checked) => setSmtpConfig(prev => ({ ...prev, use_ssl: checked }))}
                          />
                          <Label htmlFor="smtp-ssl">使用SSL加密</Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 认证信息 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">认证信息</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>SMTP用户名 *</Label>
                        <Input
                          placeholder="<EMAIL>"
                          value={smtpConfig.username}
                          onChange={(e) => setSmtpConfig(prev => ({ ...prev, username: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label>SMTP密码 *</Label>
                        <Input
                          type="password"
                          placeholder="••••••••"
                          value={smtpConfig.password}
                          onChange={(e) => setSmtpConfig(prev => ({ ...prev, password: e.target.value }))}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 发件人信息 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">发件人信息</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>发件人名称 *</Label>
                        <Input
                          placeholder="动漫网站"
                          value={smtpConfig.sender_name}
                          onChange={(e) => setSmtpConfig(prev => ({ ...prev, sender_name: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label>发件人邮箱地址 *</Label>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          value={smtpConfig.sender_email}
                          onChange={(e) => setSmtpConfig(prev => ({ ...prev, sender_email: e.target.value }))}
                        />
                      </div>
                    </div>
                  </div>

                  {/* SMTP测试工具 */}
                  <div className="space-y-4 border-t pt-4">
                    <h3 className="text-lg font-semibold">SMTP测试工具</h3>
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={testSmtpConnection}
                          disabled={smtpTesting || !smtpConfig.host || !smtpConfig.username}
                        >
                          {smtpTesting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                              测试中...
                            </>
                          ) : (
                            '测试SMTP连接'
                          )}
                        </Button>
                        <Button onClick={saveSmtpConfig}>
                          <Save className="w-4 h-4 mr-2" />
                          保存配置
                        </Button>
                      </div>

                      <div className="flex gap-2">
                        <Input
                          type="email"
                          placeholder="输入测试邮箱地址"
                          value={testEmail}
                          onChange={(e) => setTestEmail(e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          onClick={sendTestEmail}
                          disabled={emailTesting || !testEmail || !smtpConfig.host}
                        >
                          {emailTesting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                              发送中...
                            </>
                          ) : (
                            <>
                              <Mail className="w-4 h-4 mr-2" />
                              发送测试邮件
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* 配置说明 */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div className="text-sm">
                        <h4 className="font-medium text-blue-900 mb-2">配置说明：</h4>
                        <ul className="space-y-1 text-blue-800">
                          <li><strong>Gmail：</strong>smtp.gmail.com, 端口587, 启用TLS</li>
                          <li><strong>QQ邮箱：</strong>smtp.qq.com, 端口587, 启用TLS</li>
                          <li><strong>163邮箱：</strong>smtp.163.com, 端口25/994, SSL可选</li>
                          <li><strong>阿里云邮箱：</strong>smtp.mxhichina.com, 端口25/465</li>
                        </ul>
                        <p className="mt-3 text-blue-700">
                          💡 提示：Gmail需要使用应用专用密码，QQ邮箱需要开启SMTP服务并获取授权码。
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Security Settings */}
            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    安全设置
                  </CardTitle>
                  <CardDescription>配置用户注册和登录的安全策略</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 用户注册配置 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">用户注册设置</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label className="text-base">允许用户注册</Label>
                          <div className="text-sm text-muted-foreground">
                            开启后用户可以自行注册账户，关闭后只能由管理员创建账户
                          </div>
                        </div>
                        <Switch
                          checked={securityConfig.allow_registration}
                          onCheckedChange={(checked) =>
                            setSecurityConfig(prev => ({ ...prev, allow_registration: checked }))
                          }
                        />
                      </div>

                      {securityConfig.allow_registration && (
                        <>
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label className="text-base">注册时需要邮箱验证码</Label>
                              <div className="text-sm text-muted-foreground">
                                用户注册时必须通过邮箱验证码验证
                              </div>
                            </div>
                            <Switch
                              checked={securityConfig.require_email_verification}
                              onCheckedChange={(checked) =>
                                setSecurityConfig(prev => ({ ...prev, require_email_verification: checked }))
                              }
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label className="text-base">启用邮箱验证码登录</Label>
                              <div className="text-sm text-muted-foreground">
                                用户可以使用邮箱验证码登录（无需密码）
                              </div>
                            </div>
                            <Switch
                              checked={securityConfig.enable_email_login}
                              onCheckedChange={(checked) =>
                                setSecurityConfig(prev => ({ ...prev, enable_email_login: checked }))
                              }
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* 注册白名单配置 - 只有在允许用户注册时才显示 */}
                  {securityConfig.allow_registration && (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">注册白名单</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-base">启用注册白名单</Label>
                            <div className="text-sm text-muted-foreground">
                              只允许白名单内的邮箱地址注册账户
                            </div>
                          </div>
                          <Switch
                            checked={securityConfig.enable_registration_whitelist}
                            onCheckedChange={(checked) =>
                              setSecurityConfig(prev => ({ ...prev, enable_registration_whitelist: checked }))
                            }
                          />
                        </div>
                      
                      {securityConfig.enable_registration_whitelist && (
                        <div className="space-y-2">
                          <Label>白名单邮箱列表</Label>
                          <textarea
                            placeholder="输入允许注册的邮箱地址或域名，用逗号分隔&#10;例如：&#10;• 完整邮箱: <EMAIL>&#10;• 通配符: *@qq.com&#10;• 域名: qq.com, gmail.com"
                            value={securityConfig.registration_whitelist}
                            onChange={(e) => 
                              setSecurityConfig(prev => ({ ...prev, registration_whitelist: e.target.value }))
                            }
                            className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                          />
                          <div className="text-sm text-muted-foreground">
                            💡 支持格式：<br />
                            • <strong>完整邮箱</strong>: <EMAIL><br />
                            • <strong>通配符</strong>: *@qq.com（允许该域名下所有邮箱）<br />
                            • <strong>域名</strong>: qq.com（等同于 *@qq.com）
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  )}

                  {/* Cloudflare Turnstile 配置 */}
                  <div className="space-y-4 border-t pt-4">
                    <h3 className="text-lg font-semibold">Cloudflare Turnstile 验证</h3>
                    
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Turnstile 站点密钥</Label>
                          <Input
                            type="text"
                            placeholder="0x4AAAxxxxxxxxxxxxxx"
                            value={securityConfig.turnstile_site_key}
                            onChange={(e) => 
                              setSecurityConfig(prev => ({ ...prev, turnstile_site_key: e.target.value }))
                            }
                          />
                        </div>
                        <div>
                          <Label>Turnstile 密钥</Label>
                          <Input
                            type="password"
                            placeholder="0x4AAAxxxxxxxxxxxxxx"
                            value={securityConfig.turnstile_secret_key}
                            onChange={(e) => 
                              setSecurityConfig(prev => ({ ...prev, turnstile_secret_key: e.target.value }))
                            }
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <h4 className="font-medium">验证场景</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label className="text-base">用户登录时验证</Label>
                              <div className="text-sm text-muted-foreground">
                                用户登录时需要通过Turnstile验证
                              </div>
                            </div>
                            <Switch
                              checked={securityConfig.turnstile_login_enabled}
                              onCheckedChange={(checked) => 
                                setSecurityConfig(prev => ({ ...prev, turnstile_login_enabled: checked }))
                              }
                            />
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label className="text-base">用户注册时验证</Label>
                              <div className="text-sm text-muted-foreground">
                                用户注册时需要通过Turnstile验证
                              </div>
                            </div>
                            <Switch
                              checked={securityConfig.turnstile_register_enabled}
                              onCheckedChange={(checked) => 
                                setSecurityConfig(prev => ({ ...prev, turnstile_register_enabled: checked }))
                              }
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 配置说明 */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                        <div className="text-sm">
                          <h4 className="font-medium text-blue-900 mb-2">Turnstile 配置说明：</h4>
                          <ul className="space-y-1 text-blue-800">
                            <li>1. 访问 <a href="https://dash.cloudflare.com/" target="_blank" className="underline">Cloudflare Dashboard</a></li>
                            <li>2. 选择你的站点，进入 Turnstile 页面</li>
                            <li>3. 创建新的 Turnstile 站点</li>
                            <li>4. 将站点密钥和密钥复制到上方输入框</li>
                            <li>5. 根据需要启用登录或注册验证</li>
                          </ul>
                          <p className="mt-3 text-blue-700">
                            💡 提示：Turnstile 是免费的反机器人验证服务，可有效防止恶意注册和登录尝试。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 保存按钮 */}
                  <div className="flex gap-2 pt-4 border-t">
                    <Button 
                      onClick={handleSaveSecurityConfig}
                      disabled={securityConfigLoading}
                    >
                      {securityConfigLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          保存安全配置
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* SEO Settings */}
            <TabsContent value="seo" className="space-y-4">
              <SEOConfiguration />
            </TabsContent>

            {/* Advertisement Settings */}
            <TabsContent value="ads" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>广告位配置</CardTitle>
                  <CardDescription>配置网站各个位置的广告内容</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {adPlacements.map((placement) => (
                    <div key={placement.id} className="p-4 border rounded-lg space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Switch
                            checked={placement.enabled}
                            onCheckedChange={(checked) => 
                              handleUpdateAdPlacement(placement.id, { enabled: checked })
                            }
                          />
                          <div>
                            <h4 className="font-medium">{placement.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              位置: {placement.position}
                            </p>
                          </div>
                        </div>
                        <Badge variant={placement.enabled ? 'success' : 'secondary'}>
                          {placement.enabled ? '已启用' : '未启用'}
                        </Badge>
                      </div>

                      {placement.enabled && (
                        <div className="space-y-3 pl-12">
                          <div>
                            <Label>HTML代码</Label>
                            <Textarea
                              value={placement.html}
                              onChange={(e) => 
                                handleUpdateAdPlacement(placement.id, { html: e.target.value })
                              }
                              placeholder="输入广告HTML代码"
                              rows={4}
                              className="font-mono text-sm"
                            />
                          </div>
                          
                          <div>
                            <Label>JavaScript代码（可选）</Label>
                            <Textarea
                              value={placement.javascript}
                              onChange={(e) => 
                                handleUpdateAdPlacement(placement.id, { javascript: e.target.value })
                              }
                              placeholder="输入广告JavaScript代码"
                              rows={3}
                              className="font-mono text-sm"
                            />
                          </div>

                          <div className="flex items-center gap-6">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={placement.showOnMobile}
                                onCheckedChange={(checked) => 
                                  handleUpdateAdPlacement(placement.id, { showOnMobile: checked as boolean })
                                }
                              />
                              <Label>移动端显示</Label>
                            </div>
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={placement.showOnDesktop}
                                onCheckedChange={(checked) => 
                                  handleUpdateAdPlacement(placement.id, { showOnDesktop: checked as boolean })
                                }
                              />
                              <Label>桌面端显示</Label>
                            </div>
                            {placement.type === 'popup' && (
                              <div className="flex items-center gap-2">
                                <Label>显示频率:</Label>
                                <Input
                                  type="number"
                                  value={placement.frequency}
                                  onChange={(e) => 
                                    handleUpdateAdPlacement(placement.id, { 
                                      frequency: parseInt(e.target.value) || 1 
                                    })
                                  }
                                  className="w-16"
                                />
                                <span className="text-sm text-muted-foreground">次页面浏览</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  <div className="flex justify-end">
                    <Button onClick={handleSaveAdPlacements}>
                      <Save className="w-4 h-4 mr-2" />
                      保存所有广告配置
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Player Settings */}
            <TabsContent value="player" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>播放器配置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {playerConfigLoading ? (
                    <div className="text-center py-4">加载中...</div>
                  ) : (
                    <>
                      {/* 基础设置 */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">基础设置</h3>
                        <div className="flex items-center justify-between">
                          <Label>自动播放</Label>
                          <Switch
                            checked={playerConfig.autoplay}
                            onCheckedChange={(checked) =>
                              setPlayerConfig(prev => ({ ...prev, autoplay: checked }))
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>启用右键菜单</Label>
                          <Switch
                            checked={playerConfig.enable_rightclick}
                            onCheckedChange={(checked) =>
                              setPlayerConfig(prev => ({ ...prev, enable_rightclick: checked }))
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>显示统计信息</Label>
                          <Switch
                            checked={playerConfig.show_stats}
                            onCheckedChange={(checked) =>
                              setPlayerConfig(prev => ({ ...prev, show_stats: checked }))
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <Label>显示版本信息</Label>
                          <Switch
                            checked={playerConfig.show_version}
                            onCheckedChange={(checked) =>
                              setPlayerConfig(prev => ({ ...prev, show_version: checked }))
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>主题颜色</Label>
                          <Input
                            type="color"
                            value={playerConfig.theme_color}
                            onChange={(e) =>
                              setPlayerConfig(prev => ({ ...prev, theme_color: e.target.value }))
                            }
                            className="w-20 h-10"
                          />
                        </div>
                      </div>

                      {/* 广告设置 */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">广告设置</h3>
                        <div className="flex items-center justify-between">
                          <Label>启用广告</Label>
                          <Switch
                            checked={playerConfig.enable_ads}
                            onCheckedChange={(checked) =>
                              setPlayerConfig(prev => ({ ...prev, enable_ads: checked }))
                            }
                          />
                        </div>
                        {playerConfig.enable_ads && (
                          <>
                            <div className="space-y-2">
                              <Label>前贴片广告URL</Label>
                              <Input
                                value={playerConfig.preroll_ad_url}
                                onChange={(e) =>
                                  setPlayerConfig(prev => ({ ...prev, preroll_ad_url: e.target.value }))
                                }
                                placeholder="输入前贴片广告视频URL"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>中插广告URL</Label>
                              <Input
                                value={playerConfig.midroll_ad_url}
                                onChange={(e) =>
                                  setPlayerConfig(prev => ({ ...prev, midroll_ad_url: e.target.value }))
                                }
                                placeholder="输入中插广告视频URL"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>后贴片广告URL</Label>
                              <Input
                                value={playerConfig.postroll_ad_url}
                                onChange={(e) =>
                                  setPlayerConfig(prev => ({ ...prev, postroll_ad_url: e.target.value }))
                                }
                                placeholder="输入后贴片广告视频URL"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>广告跳过时间 (秒)</Label>
                              <Input
                                type="number"
                                value={playerConfig.skip_ad_time}
                                onChange={(e) =>
                                  setPlayerConfig(prev => ({ ...prev, skip_ad_time: parseInt(e.target.value) || 5 }))
                                }
                                min="0"
                                max="30"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label>广告音量 (0-1)</Label>
                              <Input
                                type="number"
                                step="0.1"
                                value={playerConfig.ad_volume}
                                onChange={(e) =>
                                  setPlayerConfig(prev => ({ ...prev, ad_volume: parseFloat(e.target.value) || 0.7 }))
                                }
                                min="0"
                                max="1"
                              />
                            </div>
                          </>
                        )}
                      </div>

                      {/* VAST广告设置 */}
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">VAST广告</h3>
                        <div className="flex items-center justify-between">
                          <Label>启用VAST广告</Label>
                          <Switch
                            checked={playerConfig.enable_vast}
                            onCheckedChange={(checked) =>
                              setPlayerConfig(prev => ({ ...prev, enable_vast: checked }))
                            }
                          />
                        </div>
                        {playerConfig.enable_vast && (
                          <div className="space-y-2">
                            <Label>VAST配置URL</Label>
                            <Input
                              value={playerConfig.vast_url}
                              onChange={(e) =>
                                setPlayerConfig(prev => ({ ...prev, vast_url: e.target.value }))
                              }
                              placeholder="输入VAST广告配置URL"
                            />
                          </div>
                        )}
                      </div>

                      <Button
                        onClick={savePlayerConfig}
                        disabled={playerConfigLoading}
                        className="w-full"
                      >
                        {playerConfigLoading ? '保存中...' : '保存播放器配置'}
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </TabsContent>
      </Tabs>
    </div>
  );
}
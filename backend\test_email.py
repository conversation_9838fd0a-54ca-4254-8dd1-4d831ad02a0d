import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import make_msgid, formatdate

# SMTP配置
smtp_config = {
    'host': 'mail.9cloud.cc',
    'port': 465,
    'use_ssl': True,
    'use_tls': False,
    'username': '<EMAIL>',
    'password': '507877550@lihao',
    'from_name': '动漫网站',
    'from_address': '<EMAIL>'
}

try:
    print('正在连接SMTP服务器...')
    if smtp_config['use_ssl']:
        server = smtplib.SMTP_SSL(smtp_config['host'], smtp_config['port'])
    else:
        server = smtplib.SMTP(smtp_config['host'], smtp_config['port'])
        if smtp_config['use_tls']:
            server.starttls()

    # 启用调试模式
    server.set_debuglevel(1)

    print('正在进行SMTP认证...')
    server.login(smtp_config['username'], smtp_config['password'])
    
    # 创建测试邮件
    msg = MIMEMultipart('alternative')
    msg['Subject'] = '测试邮件 - 动漫网站'
    msg['From'] = f"{smtp_config['from_name']} <{smtp_config['from_address']}>"
    # 测试多个邮箱
    test_emails = ['<EMAIL>']  # 可以添加更多测试邮箱

    for test_email in test_emails:
        msg['To'] = test_email
    msg['Message-ID'] = make_msgid()
    msg['Date'] = formatdate(localtime=True)
    msg['Reply-To'] = smtp_config['from_address']

    print(f"邮件头信息:")
    print(f"  From: {msg['From']}")
    print(f"  To: {msg['To']}")
    print(f"  Subject: {msg['Subject']}")
    print(f"  Message-ID: {msg['Message-ID']}")
    print(f"  Date: {msg['Date']}")
    
    # HTML内容
    html_content = '''
    <html>
    <body>
        <h2>测试邮件</h2>
        <p>这是一封来自动漫网站的测试邮件。</p>
        <p>如果您收到这封邮件，说明SMTP配置正确。</p>
        <hr>
        <p><small>此邮件由系统自动发送，请勿回复。</small></p>
    </body>
    </html>
    '''
    
    html_part = MIMEText(html_content, 'html', 'utf-8')
    msg.attach(html_part)
    
    # 添加纯文本版本
    text_content = '''
    测试邮件
    
    这是一封来自动漫网站的测试邮件。
    如果您收到这封邮件，说明SMTP配置正确。
    
    ---
    此邮件由系统自动发送，请勿回复。
    '''
    text_part = MIMEText(text_content, 'plain', 'utf-8')
    msg.attach(text_part)
    
    print('正在发送邮件...')
    send_result = server.send_message(msg)
    server.quit()
    
    if send_result:
        print(f'邮件发送有部分失败: {send_result}')
    else:
        print('邮件发送成功！请检查收件箱（包括垃圾邮件文件夹）')
        
except Exception as e:
    print(f'邮件发送失败: {str(e)}')
    import traceback
    traceback.print_exc()

from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2P<PERSON>wordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy import func
from pydantic import BaseModel, EmailStr
from app.core.database import get_db
from app.core.security import create_access_token, verify_password
from app.core.config import settings
from app.core.deps import get_current_active_user
from app.crud.user import UserCRUD
from app.schemas.user import User, UserCreate, UserLogin, Token
from app.models import User as UserModel
from app.services.verification_service import VerificationService
from app.services.turnstile_service import TurnstileService

router = APIRouter()

# 请求模型
class SendVerificationCodeRequest(BaseModel):
    email: EmailStr

class VerifyCodeRequest(BaseModel):
    email: EmailStr
    code: str

class RegisterWithCodeRequest(UserCreate):
    verification_code: str
    turnstile_token: str = ""

class LoginWithCodeRequest(BaseModel):
    email: EmailStr
    verification_code: str
    turnstile_token: str = ""

# 添加OPTIONS处理器来支持CORS预检
@router.options("/login")
async def options_login():
    return {"message": "OK"}

@router.options("/register")
async def options_register():
    return {"message": "OK"}

@router.post("/register", response_model=User, summary="用户注册")
def register(user: UserCreate, db: Session = Depends(get_db)):
    # 检查用户名是否已存在
    existing_user = UserCRUD.get_user_by_username(db, user.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    existing_email = UserCRUD.get_user_by_email(db, user.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建用户
    db_user = UserCRUD.create_user(db, user)
    return db_user

@router.post("/login", response_model=Token, summary="用户登录")
def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    # 先检查用户是否存在
    user = UserCRUD.get_user_by_username(db, form_data.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查密码是否正确
    if not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否被封禁
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被封禁，请联系管理员",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.username, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token, 
        "token_type": "bearer",
        "user": user
    }

@router.get("/me", response_model=User, summary="获取当前用户信息")
def get_me(current_user: UserModel = Depends(get_current_active_user)):
    return current_user

@router.get("/admin/stats", summary="获取管理员统计数据")
def get_admin_stats(current_user: UserModel = Depends(get_current_active_user), db: Session = Depends(get_db)):
    # 检查管理员权限
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    # 统计用户总数
    total_users = db.query(UserModel).count()
    
    # 统计动漫总数
    from app.models import Anime, Manga
    total_animes = db.query(Anime).count()
    
    # 统计漫画总数
    total_mangas = db.query(Manga).count()
    
    # 统计总观看数和总收藏数
    total_views = db.query(func.sum(Anime.view_count)).scalar() or 0
    total_favorites = db.query(func.sum(Anime.favorite_count)).scalar() or 0
    
    return {
        "total_users": total_users,
        "total_animes": total_animes,
        "total_mangas": total_mangas,
        "total_views": total_views,
        "total_favorites": total_favorites
    }

# 验证码相关端点
@router.post("/send-register-code", summary="发送注册验证码")
def send_register_verification_code(
    request: SendVerificationCodeRequest,
    db: Session = Depends(get_db)
):
    """发送注册验证码"""
    verification_service = VerificationService(db)

    # 检查邮箱是否已存在
    existing_user = UserCRUD.get_user_by_email(db, request.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )

    result = verification_service.send_register_verification_code(request.email)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )

    return {"message": result["message"]}

@router.post("/send-login-code", summary="发送登录验证码")
def send_login_verification_code(
    request: SendVerificationCodeRequest,
    db: Session = Depends(get_db)
):
    """发送登录验证码"""
    verification_service = VerificationService(db)

    # 检查邮箱是否存在
    user = UserCRUD.get_user_by_email(db, request.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="邮箱不存在"
        )

    result = verification_service.send_login_verification_code(request.email)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )

    return {"message": result["message"]}

@router.post("/register-with-code", response_model=User, summary="使用验证码注册")
def register_with_verification_code(
    request: RegisterWithCodeRequest,
    db: Session = Depends(get_db)
):
    """使用验证码注册用户"""
    verification_service = VerificationService(db)

    # 验证验证码
    verify_result = verification_service.verify_code(
        request.email,
        request.verification_code,
        "register"
    )

    if not verify_result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=verify_result["error"]
        )

    # 检查用户名是否已存在
    existing_user = UserCRUD.get_user_by_username(db, request.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    existing_email = UserCRUD.get_user_by_email(db, request.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )

    # 创建用户
    user_create = UserCreate(
        username=request.username,
        email=request.email,
        password=request.password
    )
    db_user = UserCRUD.create_user(db, user_create)
    return db_user

@router.post("/login-with-code", response_model=Token, summary="使用验证码登录")
def login_with_verification_code(
    request: LoginWithCodeRequest,
    db: Session = Depends(get_db)
):
    """使用验证码登录"""
    verification_service = VerificationService(db)

    # 验证验证码
    verify_result = verification_service.verify_code(
        request.email,
        request.verification_code,
        "login"
    )

    if not verify_result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=verify_result["error"]
        )

    # 获取用户
    user = UserCRUD.get_user_by_email(db, request.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 检查用户状态
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被封禁，请联系管理员"
        )

    # 生成访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.username, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": user
    }

@router.get("/turnstile-config", summary="获取Turnstile配置")
def get_turnstile_config(db: Session = Depends(get_db)):
    """获取Turnstile客户端配置"""
    turnstile_service = TurnstileService(db)
    return turnstile_service.get_client_config()